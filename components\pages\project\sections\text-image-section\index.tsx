import React from "react";
import TextEditor from "../text-section/text-editor";
import ImageSection from "../image-section";
import { Section, Text, ExternalLink, Image as Images } from "@prisma/client";

type Props = {
  section: Section & {
    text?: (Text & { externalLink?: ExternalLink | null }) | null;
    externalLink?: ExternalLink | null;
    image?: Images | null;
  };
};

const TextImageSection = (props: Props) => {
  return (
    <>
      <TextEditor sectionId={props.section.id} text={props.section.text} />
    </>
  );
};

export default TextImageSection;
